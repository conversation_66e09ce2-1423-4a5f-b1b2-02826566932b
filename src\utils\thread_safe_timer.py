"""
线程安全的QTimer工具类
解决QBasicTimer::start警告问题
"""

from PyQt5.QtCore import QTimer, QThread
from PyQt5.QtWidgets import QApplication
from typing import Callable, Optional
import logging

logger = logging.getLogger(__name__)


class ThreadSafeTimer:
    """线程安全的定时器工具类"""
    
    @staticmethod
    def is_main_thread() -> bool:
        """检查是否在主线程中"""
        try:
            return QThread.currentThread() == QApplication.instance().thread()
        except Exception:
            return False
    
    @staticmethod
    def create_timer(parent=None, single_shot: bool = True) -> Optional[QTimer]:
        """线程安全地创建QTimer"""
        try:
            if not ThreadSafeTimer.is_main_thread():
                logger.warning("⚠️ [线程安全] 尝试在非主线程创建QTimer，已忽略")
                return None
            
            timer = QTimer(parent)
            timer.setSingleShot(single_shot)
            return timer
            
        except Exception as e:
            logger.error(f"❌ [线程安全] 创建QTimer失败: {e}")
            return None
    
    @staticmethod
    def safe_start_timer(timer: QTimer, interval_ms: int, callback: Callable = None) -> bool:
        """线程安全地启动定时器"""
        try:
            if not timer:
                logger.warning("⚠️ [线程安全] 定时器对象为空，无法启动")
                return False
            
            if not ThreadSafeTimer.is_main_thread():
                logger.warning("⚠️ [线程安全] 尝试在非主线程启动QTimer，已忽略")
                return False
            
            if callback:
                timer.timeout.connect(callback)
            
            timer.start(interval_ms)
            logger.debug(f"✅ [线程安全] 定时器已安全启动，间隔: {interval_ms}ms")
            return True
            
        except Exception as e:
            logger.error(f"❌ [线程安全] 启动定时器失败: {e}")
            return False
    
    @staticmethod
    def safe_single_shot(interval_ms: int, callback: Callable, parent=None) -> bool:
        """线程安全的单次定时器"""
        try:
            if not ThreadSafeTimer.is_main_thread():
                logger.warning("⚠️ [线程安全] 尝试在非主线程使用singleShot，已忽略")
                return False
            
            QTimer.singleShot(interval_ms, callback)
            logger.debug(f"✅ [线程安全] singleShot已安全启动，间隔: {interval_ms}ms")
            return True
            
        except Exception as e:
            logger.error(f"❌ [线程安全] singleShot启动失败: {e}")
            return False


def safe_timer_start(timer: QTimer, interval_ms: int) -> bool:
    """便捷函数：线程安全地启动定时器"""
    return ThreadSafeTimer.safe_start_timer(timer, interval_ms)


def safe_single_shot(interval_ms: int, callback: Callable) -> bool:
    """便捷函数：线程安全的单次定时器"""
    return ThreadSafeTimer.safe_single_shot(interval_ms, callback)