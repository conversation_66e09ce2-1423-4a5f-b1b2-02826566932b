{"metadata": {"version": "1.0.0", "created_at": "2025-08-03T22:51:07.858949", "description": "统一格式管理系统配置文件", "last_modified": "2025-08-03T22:51:07.869797"}, "table_types": {"active_employees": {"display_name": "全部在职人员工资表", "description": "包含所有在职员工的工资数据", "priority": 1, "enabled": true, "default_sort": ["employee_id", "asc"]}, "retired_employees": {"display_name": "离休人员工资表", "description": "离休员工的工资数据", "priority": 2, "enabled": true, "default_sort": ["employee_id", "asc"]}, "part_time_employees": {"display_name": "临时工工资表", "description": "临时工和兼职员工的工资数据", "priority": 3, "enabled": true, "default_sort": ["employee_id", "asc"]}, "contract_employees": {"display_name": "合同工工资表", "description": "合同制员工的工资数据", "priority": 4, "enabled": true, "default_sort": ["employee_id", "asc"]}}, "default_formats": {"currency": {"decimal_places": 2, "thousand_separator": ",", "symbol": "¥", "symbol_position": "prefix", "negative_format": "-{symbol}{value}", "zero_display": "0.00"}, "percentage": {"decimal_places": 1, "symbol": "%", "symbol_position": "suffix", "multiply_by_100": false}, "integer": {"thousand_separator": ",", "zero_display": "0"}, "float": {"decimal_places": 2, "thousand_separator": ",", "zero_display": "0.00"}, "date": {"input_format": "%Y-%m-%d", "display_format": "%Y年%m月%d日", "short_format": "%Y-%m-%d"}, "string": {"max_length": 100, "trim_whitespace": true, "empty_display": ""}, "month_string": {"extract_last_two": true, "zero_pad": true, "empty_display": ""}, "month_string_extract_last_two": {"extract_last_two": true, "zero_pad": false, "empty_display": "", "format_as_string": true, "description": "提取月份后两位并转换为字符串"}, "year_string": {"prefer_four_digit": true, "empty_display": ""}}, "field_type_rules": {"currency_fields": ["position_salary", "grade_salary", "allowance", "balance_allowance", "basic_performance", "performance_bonus", "provident_fund", "housing_allowance", "car_allowance", "supplement", "advance", "total_salary", "pension_insurance", "health_fee", "transport_allowance", "property_allowance", "communication_allowance"], "integer_fields": ["employee_id", "year", "month"], "string_fields": ["employee_name", "department", "employee_type", "employee_type_code"], "date_fields": ["entry_date", "retirement_date", "birth_date"]}, "active_employees_format_config": {"table_types": ["全部在职人员", "active_employees", "全部在职人员工资表"], "field_formats": {"float_fields": ["2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"], "string_fields": ["工号", "姓名", "部门名称", "人员类别代码", "人员类别"], "special_string_fields": {"年份": "year_string", "月份": "month_string"}}, "format_rules": {"float_format": ":.2f", "string_format": "str", "null_display": "0.00", "zero_display": "0.00", "string_null_display": "", "string_dash_display": "", "remove_dot_zero": true}, "enabled": true, "priority": 1}, "retired_staff_format_config": {"table_types": ["离休人员", "retired_staff", "离休人员工资表", "retired_employees", "salary_data_2025_08_retired_employees", "salary_data_retired_employees"], "field_formats": {"float_fields": ["基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支"], "string_fields": ["姓名", "部门名称", "人员代码", "备注"], "special_string_fields": {"月份": "month_string_extract_last_two", "年份": "year_string"}}, "hidden_fields": ["创建时间", "更新时间", "自增主键", "序号", "created_at", "updated_at", "id", "row_number"], "format_rules": {"float_format": ":.2f", "string_format": "str", "null_display": "0.00", "zero_display": "0.00", "string_null_display": "", "string_dash_display": "", "none_values": ["None", "nan", "0", "0.0", "", " ", "-"], "remove_dot_zero": false}, "enabled": true, "priority": 1}, "pension_employees_format_config": {"table_types": ["退休人员工资表", "pension_employees", "退休人员", "salary_data_2025_08_pension_employees", "salary_data_pension_employees"], "field_formats": {"float_fields": ["基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款"], "string_fields": ["姓名", "部门名称", "人员代码", "备注"], "special_string_fields": {"月份": "month_string_extract_last_two", "年份": "year_string"}}, "hidden_fields": ["创建时间", "更新时间", "自增主键", "序号", "created_at", "updated_at", "id", "row_number"], "format_rules": {"float_format": ":.2f", "string_format": "str", "null_display": "0.00", "zero_display": "0.00", "string_null_display": "", "string_dash_display": "", "none_values": ["None", "nan", "0", "0.0", "", " ", "-"], "remove_dot_zero": false}, "enabled": true, "priority": 1}, "validation_rules": {"required_fields": {"active_employees": ["employee_id", "employee_name"], "retired_employees": ["employee_id", "employee_name"], "part_time_employees": ["employee_id", "employee_name"], "contract_employees": ["employee_id", "employee_name"]}, "data_ranges": {"salary_min": 0, "salary_max": 1000000, "year_min": 2020, "year_max": 2030}, "string_patterns": {"employee_id": "^[A-Za-z0-9]{6,20}$", "department": "^[\\u4e00-\\u9fa5A-Za-z0-9\\s]{1,50}$"}}, "display_rules": {"column_width": {"min_width": 80, "max_width": 300, "auto_resize": true}, "row_height": {"default_height": 25, "header_height": 30}, "color_scheme": {"header_bg": "#f0f0f0", "row_alternate": "#f9f9f9", "currency_positive": "#000000", "currency_negative": "#cc0000"}, "font_settings": {"family": "微软雅黑", "size": 9, "bold_headers": true}}, "export_settings": {"excel": {"sheet_name_template": "{table_type}_{date}", "include_metadata": true, "freeze_header": true}, "csv": {"encoding": "utf-8-sig", "separator": ",", "include_header": true}}}