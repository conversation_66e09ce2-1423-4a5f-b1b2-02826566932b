#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
请求去重管理器
遵循CLAUDE.md要求：数据流追踪日志，消除重复请求
"""

import time
import hashlib
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from collections import defaultdict

from src.utils.log_config import setup_logger


@dataclass
class RequestRecord:
    """请求记录"""
    request_key: str
    timestamp: float
    table_name: str
    page: int
    sort_columns: Optional[List[Dict]]
    request_id: str


class RequestDeduplicationManager:
    """
    请求去重管理器

    防止短时间内的重复请求，提升系统性能
    遵循CLAUDE.md原则：数据流追踪日志 + 性能优化

    🔧 [立即修复] 强化分页请求去重机制，解决分页循环调用问题
    """

    def __init__(self, default_ttl_seconds: float = 0.3):
        """
        初始化请求去重管理器

        Args:
            default_ttl_seconds: 默认去重时间窗口（秒）- 缩短到0.3秒提高响应性
        """
        self.logger = setup_logger(self.__class__.__name__)
        self._default_ttl = default_ttl_seconds
        self._active_requests = {}  # 活跃请求记录
        self._request_history = []  # 请求历史记录
        self._lock = threading.RLock()  # 使用可重入锁，防止死锁

        # 🔧 [立即修复] 增强统计信息
        self._stats = {
            'total_requests': 0,
            'duplicated_requests': 0,
            'allowed_requests': 0,
            'pagination_loops_prevented': 0,  # 防止的分页循环次数
            'rapid_requests_blocked': 0       # 阻止的快速重复请求
        }

        # 🔧 [立即修复] 分页专用去重机制
        self._pagination_requests = {}  # 分页请求专用记录
        self._pagination_ttl = 0.2      # 分页请求更短的TTL

        self.logger.info(f"请求去重管理器初始化完成，默认TTL: {default_ttl_seconds}秒")
    
    def should_allow_pagination_request(self, table_name: str, page: int,
                                      sort_columns: Optional[List[Dict]] = None) -> bool:
        """
        🔧 [立即修复] 专门的分页请求去重检查

        使用更严格的去重策略防止分页循环调用

        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列信息

        Returns:
            True: 允许分页请求, False: 拒绝重复分页请求
        """
        current_time = time.time()

        # 生成分页专用请求键
        pagination_key = f"pagination_{self.generate_request_key(table_name, page, sort_columns)}"

        with self._lock:
            self._stats['total_requests'] += 1

            # 数据流追踪日志：分页请求输入
            self.logger.debug(f"🔧 [分页去重] 检查分页请求: 表={table_name}, 页={page}, "
                            f"排序列={len(sort_columns) if sort_columns else 0}个")

            # 检查分页专用记录
            if pagination_key in self._pagination_requests:
                last_request = self._pagination_requests[pagination_key]
                time_diff = current_time - last_request.timestamp

                if time_diff < self._pagination_ttl:
                    # 分页循环，拒绝
                    self._stats['duplicated_requests'] += 1
                    self._stats['pagination_loops_prevented'] += 1

                    self.logger.info(f"🔧 [分页去重] 防止分页循环: 表={table_name}, 页={page}, "
                                   f"时间间隔={time_diff:.3f}s < TTL={self._pagination_ttl}s")

                    return False
                else:
                    # 已过期，可以处理新分页请求
                    self.logger.debug(f"🔧 [分页去重] 分页请求已过期: 时间间隔={time_diff:.3f}s")

            # 记录新分页请求
            request_record = RequestRecord(
                request_key=pagination_key,
                timestamp=current_time,
                table_name=table_name,
                page=page,
                sort_columns=sort_columns,
                request_id=f"page_req_{int(current_time * 1000000)}"
            )

            self._pagination_requests[pagination_key] = request_record
            self._stats['allowed_requests'] += 1

            # 数据流追踪日志：分页请求允许
            self.logger.debug(f"🔧 [分页去重] 允许分页请求: 表={table_name}, 页={page}, "
                            f"请求ID={request_record.request_id}")

            return True

    def should_allow_request(self, table_name: str, page: int,
                           sort_columns: Optional[List[Dict]] = None,
                           ttl_seconds: Optional[float] = None) -> bool:
        """
        检查是否应该允许请求

        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列信息
            ttl_seconds: 自定义TTL，None使用默认值

        Returns:
            True: 允许请求, False: 拒绝重复请求
        """
        current_time = time.time()
        effective_ttl = ttl_seconds or self._default_ttl

        # 生成请求键
        request_key = self.generate_request_key(table_name, page, sort_columns)

        with self._lock:
            self._stats['total_requests'] += 1

            # 数据流追踪日志：输入参数
            self.logger.debug(f"[数据流追踪] 去重检查输入: 表={table_name}, 页={page}, "
                            f"排序列={len(sort_columns) if sort_columns else 0}个, TTL={effective_ttl}s")

            # 检查是否有相同的活跃请求
            if request_key in self._active_requests:
                last_request = self._active_requests[request_key]
                time_diff = current_time - last_request.timestamp

                if time_diff < effective_ttl:
                    # 重复请求，拒绝
                    self._stats['duplicated_requests'] += 1

                    self.logger.info(f"[数据流追踪] 拒绝重复请求: {request_key[:16]}..., "
                                   f"时间间隔={time_diff:.3f}s < TTL={effective_ttl}s")

                    return False
                else:
                    # 已过期，可以处理新请求
                    self.logger.debug(f"[数据流追踪] 旧请求已过期: 时间间隔={time_diff:.3f}s")

            # 记录新请求
            request_record = RequestRecord(
                request_key=request_key,
                timestamp=current_time,
                table_name=table_name,
                page=page,
                sort_columns=sort_columns,
                request_id=f"req_{int(current_time * 1000000)}"
            )

            self._active_requests[request_key] = request_record
            self._request_history.append(request_record)

            # 保持历史记录在合理范围内
            if len(self._request_history) > 1000:
                self._request_history = self._request_history[-500:]

            self._stats['allowed_requests'] += 1

            # 数据流追踪日志：输出结果
            self.logger.debug(f"[数据流追踪] 允许请求: {request_key[:16]}..., "
                            f"请求ID={request_record.request_id}")

            return True
    
    def generate_request_key(self, table_name: str, page: int, 
                           sort_columns: Optional[List[Dict]] = None) -> str:
        """
        生成请求键
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列信息
            
        Returns:
            请求键字符串
        """
        # 构建排序键
        sort_key = ""
        if sort_columns:
            sort_parts = []
            for col in sort_columns:
                if isinstance(col, dict):
                    col_name = col.get('column_name', '')
                    order = col.get('order', 'asc')
                    priority = col.get('priority', 0)
                    sort_parts.append(f"{col_name}:{order}:{priority}")
            sort_key = "|".join(sort_parts)
        
        # 组合请求信息
        request_info = f"{table_name}:p{page}:s{sort_key}"
        
        # 生成哈希键
        request_key = hashlib.md5(request_info.encode('utf-8')).hexdigest()
        
        self.logger.debug(f"[数据流追踪] 生成请求键: {request_info} -> {request_key[:16]}...")
        
        return request_key
    
    def clear_expired_requests(self, max_age_seconds: float = 5.0):
        """
        清理过期的请求记录

        Args:
            max_age_seconds: 最大保留时间（秒）
        """
        current_time = time.time()

        with self._lock:
            expired_keys = []
            expired_pagination_keys = []

            # 清理普通请求
            for request_key, request_record in self._active_requests.items():
                age = current_time - request_record.timestamp
                if age > max_age_seconds:
                    expired_keys.append(request_key)

            # 🔧 [立即修复] 清理分页请求（使用更短的过期时间）
            pagination_max_age = min(max_age_seconds, 2.0)  # 分页请求最多保留2秒
            for pagination_key, request_record in self._pagination_requests.items():
                age = current_time - request_record.timestamp
                if age > pagination_max_age:
                    expired_pagination_keys.append(pagination_key)

            # 清理过期请求
            for key in expired_keys:
                del self._active_requests[key]

            for key in expired_pagination_keys:
                del self._pagination_requests[key]

            if expired_keys or expired_pagination_keys:
                self.logger.debug(f"🔧 [清理] 清理过期请求: 普通{len(expired_keys)}个, 分页{len(expired_pagination_keys)}个")
    
    def mark_pagination_request_completed(self, table_name: str, page: int,
                                        sort_columns: Optional[List[Dict]] = None):
        """
        🔧 [立即修复] 标记分页请求已完成

        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列信息
        """
        pagination_key = f"pagination_{self.generate_request_key(table_name, page, sort_columns)}"

        with self._lock:
            if pagination_key in self._pagination_requests:
                del self._pagination_requests[pagination_key]
                self.logger.debug(f"🔧 [分页完成] 标记分页请求完成: 表={table_name}, 页={page}")

    def mark_request_completed(self, table_name: str, page: int,
                             sort_columns: Optional[List[Dict]] = None):
        """
        标记请求已完成

        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列信息
        """
        request_key = self.generate_request_key(table_name, page, sort_columns)

        with self._lock:
            if request_key in self._active_requests:
                del self._active_requests[request_key]
                self.logger.debug(f"[数据流追踪] 标记请求完成: {request_key[:16]}...")
    
    def get_deduplication_statistics(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        with self._lock:
            stats = self._stats.copy()
            stats['active_requests_count'] = len(self._active_requests)
            stats['history_count'] = len(self._request_history)
            
            # 计算去重率
            if stats['total_requests'] > 0:
                stats['deduplication_rate'] = (stats['duplicated_requests'] / stats['total_requests']) * 100
            else:
                stats['deduplication_rate'] = 0.0
            
            return stats
    
    def get_active_requests(self) -> List[Dict[str, Any]]:
        """获取当前活跃请求列表"""
        with self._lock:
            current_time = time.time()
            active_list = []
            
            for request_record in self._active_requests.values():
                age = current_time - request_record.timestamp
                active_list.append({
                    'table_name': request_record.table_name,
                    'page': request_record.page,
                    'sort_columns_count': len(request_record.sort_columns) if request_record.sort_columns else 0,
                    'age_seconds': round(age, 3),
                    'request_id': request_record.request_id
                })
            
            return active_list
    
    def configure_ttl(self, default_ttl_seconds: float):
        """
        配置默认TTL
        
        Args:
            default_ttl_seconds: 新的默认TTL（秒）
        """
        old_ttl = self._default_ttl
        self._default_ttl = default_ttl_seconds
        
        self.logger.info(f"[配置更新] 默认TTL: {old_ttl}s -> {default_ttl_seconds}s")
    
    def force_clear_all_requests(self):
        """强制清空所有请求记录"""
        with self._lock:
            active_count = len(self._active_requests)
            self._active_requests.clear()
            
            self.logger.info(f"[数据流追踪] 强制清空所有活跃请求: {active_count}个")


class SmartDeduplicationManager(RequestDeduplicationManager):
    """
    智能去重管理器
    
    基于数据量和操作类型自动调整去重策略
    """
    
    def __init__(self):
        """初始化智能去重管理器"""
        super().__init__(default_ttl_seconds=0.3)
        
        # 🔧 [P2性能修复] 基于操作类型的TTL配置 - 增加分页去重时间窗口
        self._operation_ttl_config = {
            'pagination': 2.0,    # 分页操作 - 增加到2秒，防止连续点击
            'sort': 1.0,          # 排序操作 - 增加到1秒，排序比较耗时
            'refresh': 1.5,       # 刷新操作
            'filter': 0.8         # 过滤操作
        }
        
        self.logger.info("智能去重管理器初始化完成")
    
    def should_allow_request_smart(self, table_name: str, page: int,
                                 sort_columns: Optional[List[Dict]] = None,
                                 operation_type: str = 'pagination',
                                 data_size_hint: Optional[int] = None) -> bool:
        """
        智能去重检查
        
        Args:
            table_name: 表名
            page: 页码
            sort_columns: 排序列信息
            operation_type: 操作类型 ('pagination', 'sort', 'refresh', 'filter')
            data_size_hint: 数据大小提示
            
        Returns:
            True: 允许请求, False: 拒绝重复请求
        """
        # 根据操作类型选择TTL
        base_ttl = self._operation_ttl_config.get(operation_type, self._default_ttl)
        
        # 根据数据大小调整TTL
        if data_size_hint:
            if data_size_hint > 1000:
                # 大数据集，增加TTL
                adjusted_ttl = base_ttl * 1.5
            elif data_size_hint < 100:
                # 小数据集，减少TTL
                adjusted_ttl = base_ttl * 0.7
            else:
                adjusted_ttl = base_ttl
        else:
            adjusted_ttl = base_ttl
        
        self.logger.debug(f"[数据流追踪] 智能TTL计算: 操作类型={operation_type}, "
                        f"基础TTL={base_ttl}s, 调整后TTL={adjusted_ttl:.3f}s")
        
        return self.should_allow_request(table_name, page, sort_columns, adjusted_ttl)


# 全局单例实例
_request_dedup_manager = None


def get_request_deduplication_manager() -> SmartDeduplicationManager:
    """获取请求去重管理器单例实例"""
    global _request_dedup_manager
    if _request_dedup_manager is None:
        _request_dedup_manager = SmartDeduplicationManager()
    return _request_dedup_manager