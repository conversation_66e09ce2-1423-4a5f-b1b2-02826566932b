"""
表格数据服务层 - 架构重构服务层

整合统一数据请求管理器、状态管理器和事件总线，
提供高级的表格数据管理服务。

主要功能：
1. 统一的表格数据请求接口
2. 自动状态管理和同步
3. 事件驱动的数据更新
4. 错误处理和恢复

架构目标：
- 隐藏底层复杂性
- 提供简单易用的API
- 确保数据一致性
- 支持并发和缓存
"""

from typing import Dict, Any, List, Optional, Callable
import pandas as pd
from datetime import datetime
import time # Added for time.time()

from src.utils.log_config import setup_logger
from src.core.unified_data_request_manager import (
    UnifiedDataRequestManager, DataRequest, DataResponse, RequestType
)
from src.core.unified_state_manager import (
    UnifiedStateManager, StateListener, StateChange, StateChangeType
)
from src.core.event_bus import (
    get_event_bus, EventBus, SortRequestEvent, PageRequestEvent, 
    DataUpdatedEvent, FieldMappingChangeEvent, NavigationChangeEvent
)
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config import ConfigManager
# 🎯 [统一格式管理] 导入统一格式管理系统
# 🎯 [统一格式管理] 使用统一格式管理系统


class TableDataService(StateListener):
    """表格数据服务层"""
    
    def __init__(self, 
                 db_manager: DynamicTableManager,
                 config_manager: ConfigManager):
        self.logger = setup_logger("TableDataService")
        
        # 核心组件
        self.db_manager = db_manager
        self.config_manager = config_manager
        
        # 架构重构组件
        self.data_request_manager = UnifiedDataRequestManager(db_manager, config_manager)
        self.state_manager = UnifiedStateManager()
        self.event_bus = get_event_bus()
        
        # 🎯 [统一格式管理] 格式化功能通过统一格式管理器处理
        
        # 服务状态
        self._current_table = None
        self._data_cache = {}
        
        # 🚀 [性能监控] 缓存统计
        self._cache_stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
        self._loading_states = {}  # 跟踪加载状态
        
        # 注册为状态监听器
        self.state_manager.add_state_listener(self)
        
        # 订阅事件
        self._subscribe_to_events()
        
        self.logger.info("表格数据服务初始化完成")
    
    def _subscribe_to_events(self):
        """订阅相关事件"""
        # 订阅排序请求事件
        self.event_bus.subscribe(
            "sort_request",
            self._handle_sort_request,
            async_handler=True
        )
        
        # 订阅分页请求事件
        self.event_bus.subscribe(
            "page_request", 
            self._handle_page_request,
            async_handler=True
        )
        
        # 订阅字段映射变更事件
        self.event_bus.subscribe(
            "field_mapping_change",
            self._handle_field_mapping_change,
            async_handler=True
        )
        
        # 订阅导航变更事件
        self.event_bus.subscribe(
            "navigation_change",
            self._handle_navigation_change,
            async_handler=True
        )
        
        self.logger.debug("已订阅所有相关事件")
    
    # StateListener 接口实现
    def on_table_state_changed(self, table_name: str, table_state, change: StateChange):
        """表状态变更回调"""
        try:
            self.logger.debug(f"表状态变更: {table_name}, 类型: {change.change_type}")
            
            # 根据变更类型进行相应处理
            if change.change_type == StateChangeType.SORT_CHANGED:
                self._invalidate_cache(table_name)
            elif change.change_type == StateChangeType.PAGE_CHANGED:
                self._refresh_page_data(table_name)
            elif change.change_type == StateChangeType.FIELD_MAPPING_CHANGED:
                self._invalidate_cache(table_name)
                self._refresh_all_data(table_name)
            
        except Exception as e:
            self.logger.error(f"处理表状态变更失败: {e}")
    
    def on_global_state_changed(self, global_state, change: StateChange):
        """全局状态变更回调"""
        try:
            self.logger.debug(f"全局状态变更: {change.change_type}")
            
            if change.change_type == StateChangeType.TABLE_SWITCHED:
                self._handle_table_switch(global_state.current_table)
                
        except Exception as e:
            self.logger.error(f"处理全局状态变更失败: {e}")
    
    # 事件处理器
    def _handle_sort_request(self, event: SortRequestEvent):
        """处理排序请求"""
        try:
            self.logger.info(f"[排序调试] TableDataService接收到排序请求: {event.table_name}")
            self.logger.info(f"[排序调试] 排序列: {event.sort_columns}")
            self.logger.info(f"[排序调试] 当前页: {event.current_page}")
            
            # 验证事件数据
            if not event.table_name:
                self.logger.error("[排序调试] 排序请求事件表名为空")
                return
                
            if not event.sort_columns:
                self.logger.info("[排序调试] 收到清空排序请求，将清理排序状态")
                # 允许继续处理，实现清空排序功能
            
            # 更新状态
            self.state_manager.update_table_state(
                event.table_name,
                {
                    "sort_columns": event.sort_columns,
                    "current_page": event.current_page  # 排序时可能需要重置页码
                },
                StateChangeType.SORT_CHANGED
            )
            
            # 创建数据请求
            request = DataRequest(
                table_name=event.table_name,
                request_type=RequestType.SORT_CHANGE,
                page=event.current_page,
                sort_columns=event.sort_columns,
                preserve_page=True  # 🔧 [CRITICAL修复] 排序时保持当前页码，不重置
            )
            
            self.logger.info(f"[排序调试] 执行数据请求: {request}")
            
            # 更新缓存统计
            self._cache_stats["misses"] += 1
            self._cache_stats["total_requests"] += 1
                
            # 执行请求
            response = self.data_request_manager.request_table_data(request)
            
            self.logger.info(f"[排序调试] 数据请求响应: 成功={response.success}")
            
            # 🔧 [修复排序] 恢复事件发布，确保UI能接收到排序结果
            if response.success:
                self.logger.info(f"[排序调试] 排序数据获取成功: {len(response.data)}行")
                self.logger.info(f"[修复排序] 排序操作成功，发布数据更新事件: {event.table_name}, {len(response.data)}行")
                
                # 构建数据更新事件
                from src.core.event_bus import DataUpdatedEvent
                data_event = DataUpdatedEvent(
                    event_type="data_updated",
                    table_name=event.table_name,
                    data=response.data,
                    metadata={
                        'request_type': 'sort_change',
                        'current_page': response.current_page,
                        'page_size': response.page_size,
                        'total_records': response.total_records,
                        'sort_columns': event.sort_columns,
                        'timestamp': response.timestamp
                    }
                )
                
                # 发布事件，让UI组件接收更新
                self.event_bus.publish(data_event)
                self.logger.info(f"[修复排序] 数据更新事件已发布")
                self.logger.info(f"[排序调试] 数据更新事件已发布")
            else:
                self.logger.error(f"[排序调试] 排序请求处理失败: {response.error}")
                
        except Exception as e:
            self.logger.error(f"[排序调试] 处理排序请求异常: {e}")
            import traceback
            traceback.print_exc()
    
    def _handle_page_request(self, event: PageRequestEvent):
        """处理分页请求"""
        try:
            self.logger.info(f"处理分页请求: {event.table_name}, 页码: {event.page}")
            
            # 获取当前状态
            table_state = self.state_manager.get_table_state(event.table_name)
            
            # 更新状态
            self.state_manager.update_table_state(
                event.table_name,
                {
                    "current_page": event.page,
                    "page_size": event.page_size
                },
                StateChangeType.PAGE_CHANGED
            )
            
            # 创建数据请求
            request = DataRequest(
                table_name=event.table_name,
                request_type=RequestType.PAGE_CHANGE,
                page=event.page,
                page_size=event.page_size,
                sort_columns=table_state.sort_columns if event.preserve_sort else None,
                preserve_sort=event.preserve_sort
            )
            
            # 执行请求
            response = self.data_request_manager.request_table_data(request)
            
            # 🔧 [修复分页] 恢复事件发布，确保UI能接收到分页结果
            if response.success:
                self.logger.info(f"[修复分页] 分页操作成功，发布数据更新事件: {event.table_name}, {len(response.data)}行")
                
                # 构建数据更新事件
                from src.core.event_bus import DataUpdatedEvent
                data_event = DataUpdatedEvent(
                    event_type="data_updated",
                    table_name=event.table_name,
                    data=response.data,
                    metadata={
                        'request_type': 'page_change',
                        'current_page': response.current_page,
                        'page_size': response.page_size,
                        'total_records': response.total_records,
                        'sort_columns': table_state.sort_columns if event.preserve_sort else [],
                        'timestamp': response.timestamp
                    }
                )
                
                # 发布事件，让UI组件接收更新
                self.event_bus.publish(data_event)
                self.logger.info(f"[修复分页] 数据更新事件已发布")
            else:
                self.logger.error(f"分页请求处理失败: {response.error}")
                
        except Exception as e:
            self.logger.error(f"处理分页请求失败: {e}")
    
    def _handle_field_mapping_change(self, event: FieldMappingChangeEvent):
        """处理字段映射变更"""
        try:
            self.logger.info(f"处理字段映射变更: {event.table_name}")
            
            # 更新状态
            self.state_manager.update_table_state(
                event.table_name,
                {"field_mapping": event.new_mapping},
                StateChangeType.FIELD_MAPPING_CHANGED
            )
            
            # 刷新当前数据
            self._refresh_current_data(event.table_name)
            
        except Exception as e:
            self.logger.error(f"处理字段映射变更失败: {e}")
    
    def _handle_navigation_change(self, event: NavigationChangeEvent):
        """处理导航变更"""
        try:
            self.logger.info(f"处理导航变更: {event.old_path} -> {event.new_path}")
            
            # 从导航路径提取表名
            table_name = self._extract_table_name_from_path(event.new_path)
            
            if table_name:
                # 更新全局状态
                self.state_manager.update_global_state(
                    {
                        "current_table": table_name,
                        "navigation_path": event.new_path.split("/")
                    },
                    StateChangeType.TABLE_SWITCHED
                )
                
                # 加载新表数据
                self.load_table_data(table_name)
            
        except Exception as e:
            self.logger.error(f"处理导航变更失败: {e}")
    
    # 公共API
    def load_table_data(self, 
                       table_name: str, 
                       page: int = 1, 
                       page_size: int = 50,
                       sort_columns: Optional[List[Dict[str, Any]]] = None,
                       force_reload: bool = False) -> DataResponse:
        """
        加载表格数据
        
        Args:
            table_name: 表名
            page: 页码
            page_size: 页大小
            sort_columns: 排序列
            force_reload: 强制重新加载
            
        Returns:
            DataResponse: 数据响应
        """
        try:
            self.logger.info(f"加载表格数据: {table_name}, 页码: {page}")
            
            # [性能优化] 简化缓存键生成，减少计算开销
            if sort_columns:
                # 快速生成排序键，避免复杂的标准化过程
                sort_parts = [f"{col.get('column_name', '')}{col.get('order', 'asc')}" for col in sort_columns if col.get('column_name')]
                sort_key = "_".join(sort_parts)
            else:
                sort_key = "nosort"
            
            # 简化缓存键，避免MD5计算开销
            cache_key = f"tbl_{table_name}_p{page}_s{page_size}_{sort_key}"
            if not force_reload and cache_key in self._data_cache:
                cached_response = self._data_cache[cache_key]
                cache_time = getattr(cached_response, '_cache_time', None)
                
                # [性能修复] 智能缓存策略
                cache_ttl = 300  # 5分钟缓存
                current_time = time.time()
                
                if cache_time and (current_time - cache_time) < cache_ttl:
                    self._cache_stats["hits"] += 1
                    self._cache_stats["total_requests"] += 1
                    self.logger.info(f"[缓存命中] 使用缓存数据: {table_name} 第{page}页")
                    
                    # 🔧 [P1性能修复] 标记缓存命中，用于优化UI显示
                    cached_response.cache_hit = True
                    
                    # 🔧 [根本修复] 缓存命中时必须发布数据更新事件，确保UI能收到数据
                    self.logger.info(f"[根本修复] 缓存命中，发布数据更新事件: {table_name}, {len(cached_response.data) if cached_response.data is not None else 0}行")
                    
                    # 构建缓存数据更新事件
                    from src.core.event_bus import DataUpdatedEvent
                    data_event = DataUpdatedEvent(
                        event_type="data_updated",
                        table_name=table_name,
                        data=cached_response.data,
                        metadata={
                            'request_type': 'cached_load',
                            'current_page': page,
                            'page_size': cached_response.page_size if hasattr(cached_response, 'page_size') else page_size,
                            'total_records': cached_response.total_records if hasattr(cached_response, 'total_records') else 0,
                            'sort_columns': sort_columns or [],
                            'from_cache': True,
                            'timestamp': datetime.now(),
                            'cache_hit': True
                        }
                    )
                    
                    # 发布缓存数据更新事件
                    self.event_bus.publish(data_event)
                    self.logger.info(f"[根本修复] 缓存数据更新事件已发布，UI应该能正常更新")
                    
                    return cached_response
                else:
                    # 缓存过期，移除
                    del self._data_cache[cache_key]
                    self.logger.debug(f"[缓存过期] 清理过期缓存: {cache_key}")
            
            # 标记加载状态
            self._loading_states[table_name] = True
            
            try:
                # 创建数据请求
                request = DataRequest(
                    table_name=table_name,
                    request_type=RequestType.INITIAL_LOAD,
                    page=page,
                    page_size=page_size,
                    sort_columns=sort_columns
                )
                
                # 执行请求
                response = self.data_request_manager.request_table_data(request)
                
                # 更新状态
                if response.success:
                    # [统一格式化] 使用单例化的主格式化管理器
                    try:
                        if response.data is not None and not response.data.empty:
                            # 检测表格类型（从表名中提取）
                            table_type = self._extract_table_type_from_name(table_name)
                            
                            # [新架构] 使用统一格式管理器（内置单例优化）
                            from src.modules.format_management.unified_format_manager import get_unified_format_manager
                            unified_formatter = get_unified_format_manager()
                            self.logger.info(f"[新架构] 使用统一格式管理器（单例优化）")
                            
                            # 统一格式化处理
                            formatted_data = unified_formatter.format_table_data(
                                response.data, table_type
                            )
                            response.data = formatted_data
                            
                            # 标记数据已格式化
                            if hasattr(response.data, 'attrs'):
                                response.data.attrs['formatted'] = True
                            
                            self.logger.info(f"[统一格式化] 数据格式化完成: {table_name}, 类型: {table_type}")
                        
                    except Exception as format_error:
                        self.logger.error(f"[统一格式化] 数据格式化失败: {format_error}")
                        # 格式化失败不影响数据加载，继续使用原始数据
                    
                    self.state_manager.update_table_state(
                        table_name,
                        {
                            "current_page": response.current_page,
                            "page_size": response.page_size,
                            "total_records": response.total_records,
                            "sort_columns": sort_columns or [],
                            "last_loaded": datetime.now()
                        }
                    )
                    
                    # [性能修复] 缓存响应并添加时间戳
                    setattr(response, '_cache_time', time.time())
                    self._data_cache[cache_key] = response
                    
                    # [性能修复] LRU缓存清理 - 限制缓存大小
                    max_cache_size = 50
                    if len(self._data_cache) > max_cache_size:
                        # 移除最老的缓存条目
                        oldest_key = None
                        oldest_time = float('inf')
                        for key, cached_resp in self._data_cache.items():
                            cache_time = getattr(cached_resp, '_cache_time', 0)
                            if cache_time < oldest_time:
                                oldest_time = cache_time
                                oldest_key = key
                        if oldest_key:
                            del self._data_cache[oldest_key]
                            self.logger.debug(f"[LRU清理] 移除最老缓存: {oldest_key}")
                    
                    # [修复数据发布] 恢复数据更新事件发布，确保UI及时更新
                    self.logger.info(f"[修复数据发布] 数据加载成功，发布更新事件: {table_name}, {len(response.data)}行")
                    
                    # 构建数据更新事件
                    from src.core.event_bus import DataUpdatedEvent
                    data_event = DataUpdatedEvent(
                        event_type="data_updated",
                        table_name=table_name,
                        data=response.data,
                        metadata={
                            'request_type': request.request_type.value if hasattr(request.request_type, 'value') else str(request.request_type),
                            'current_page': response.current_page,
                            'page_size': response.page_size,
                            'total_records': response.total_records,
                            'sort_columns': sort_columns or [],
                            'timestamp': response.timestamp,
                            'from_cache': False
                        }
                    )
                    
                    # 发布数据更新事件
                    self.event_bus.publish(data_event)
                    self.logger.info(f"[修复数据发布] 数据更新事件已发布")
                
                return response
                
            except Exception as inner_e:
                self.logger.error(f"数据请求处理失败: {inner_e}")
                raise inner_e
            finally:
                # 清除加载状态
                self._loading_states[table_name] = False
                
        except Exception as e:
            self.logger.error(f"加载表格数据失败: {e}", exc_info=True)
            self.logger.error(f"异常类型: {type(e)}, 异常值: {repr(e)}")
            self._loading_states[table_name] = False
            return DataResponse(
                success=False,
                table_name=table_name,
                error=str(e),
                error_code="LOAD_ERROR"
            )
    
    def refresh_table_data(self, table_name: str) -> DataResponse:
        """刷新表格数据"""
        try:
            # 获取当前状态
            table_state = self.state_manager.get_table_state(table_name)
            
            # 清除缓存
            self._invalidate_cache(table_name)
            
            # 重新加载数据
            return self.load_table_data(
                table_name=table_name,
                page=table_state.current_page,
                page_size=table_state.page_size,
                sort_columns=table_state.sort_columns,
                force_reload=True
            )
            
        except Exception as e:
            self.logger.error(f"刷新表格数据失败: {e}")
            return DataResponse(
                success=False,
                table_name=table_name,
                error=str(e),
                error_code="REFRESH_ERROR"
            )
    
    def get_table_state(self, table_name: str) -> Dict[str, Any]:
        """获取表状态"""
        try:
            table_state = self.state_manager.get_table_state(table_name)
            return {
                "table_name": table_state.table_name,
                "current_page": table_state.current_page,
                "page_size": table_state.page_size,
                "total_records": table_state.total_records,
                "sort_columns": table_state.sort_columns,
                "active_filters": table_state.active_filters,
                "selected_rows": table_state.selected_rows,
                "last_loaded": table_state.last_loaded.isoformat() if table_state.last_loaded else None,
                "is_loading": self._loading_states.get(table_name, False)
            }
        except Exception as e:
            self.logger.error(f"获取表状态失败: {e}")
            return {}
    
    def get_table_record_count(self, table_name: str) -> int:
        """[分页修复] 获取表的总记录数"""
        try:
            # 通过数据请求管理器获取记录数
            if hasattr(self.data_request_manager, 'get_table_record_count'):
                count = self.data_request_manager.get_table_record_count(table_name)
                self.logger.debug(f"[分页修复] 表 {table_name} 总记录数: {count}")
                return count
            
            # 后备方案：通过db_manager获取
            if hasattr(self, 'db_manager') and self.db_manager:
                if hasattr(self.db_manager, 'get_table_record_count'):
                    count = self.db_manager.get_table_record_count(table_name)
                    self.logger.debug(f"[分页修复] 表 {table_name} 总记录数(db_manager): {count}")
                    return count
            
            # 最后的后备方案：检查表状态
            table_state = self.state_manager.get_table_state(table_name)
            if table_state and table_state.total_records > 0:
                count = table_state.total_records
                self.logger.debug(f"[分页修复] 表 {table_name} 总记录数(状态): {count}")
                return count
            
            self.logger.warning(f"[分页修复] 无法获取表 {table_name} 的总记录数")
            return 0
                
        except Exception as e:
            self.logger.error(f"[分页修复] 获取表记录数异常: {e}")
            return 0
    
    def is_table_loading(self, table_name: str) -> bool:
        """检查表是否正在加载"""
        return self._loading_states.get(table_name, False)
    
    def _safe_get_field_count(self, data) -> int:
        """安全地获取字段数量"""
        try:
            if data is None:
                return 0
            
            # 检查是否为pandas DataFrame
            if hasattr(data, 'columns'):
                return len(data.columns)
            
            # 检查是否为列表
            if isinstance(data, list):
                if len(data) == 0:
                    return 0
                first_row = data[0]
                if isinstance(first_row, dict):
                    return len(first_row)
                elif hasattr(first_row, '__len__'):
                    return len(first_row)
                elif hasattr(first_row, '__dict__'):
                    return len(first_row.__dict__)
            
            return 0
        except Exception as e:
            self.logger.warning(f"计算字段数量时出错: {e}")
            return 0
    
    # 私有方法
    def _invalidate_cache(self, table_name: str):
        """使指定表的缓存失效"""
        keys_to_remove = [key for key in self._data_cache.keys() if key.startswith(f"{table_name}_")]
        for key in keys_to_remove:
            del self._data_cache[key]
        self.logger.debug(f"已清除表 {table_name} 的缓存")
    
    def _is_cache_valid(self, cached_response: DataResponse, ttl_seconds: int = 300) -> bool:
        """检查缓存是否有效"""
        if not cached_response.timestamp:
            return False
        
        age = (datetime.now() - cached_response.timestamp).total_seconds()
        return age < ttl_seconds
    
    def _refresh_page_data(self, table_name: str):
        """刷新页面数据"""
        try:
            table_state = self.state_manager.get_table_state(table_name)
            self.load_table_data(
                table_name=table_name,
                page=table_state.current_page,
                page_size=table_state.page_size,
                sort_columns=table_state.sort_columns,
                force_reload=True
            )
        except Exception as e:
            self.logger.error(f"刷新页面数据失败: {e}")
    
    def _refresh_all_data(self, table_name: str):
        """刷新所有数据"""
        try:
            self._invalidate_cache(table_name)
            self._refresh_page_data(table_name)
        except Exception as e:
            self.logger.error(f"刷新所有数据失败: {e}")
    
    def _refresh_current_data(self, table_name: str):
        """刷新当前数据"""
        self._refresh_page_data(table_name)
    
    def _handle_table_switch(self, new_table: str):
        """处理表切换"""
        try:
            if new_table != self._current_table:
                self.logger.info(f"切换表: {self._current_table} -> {new_table}")
                self._current_table = new_table
                
                if new_table:
                    # 预加载新表数据
                    self.load_table_data(new_table)
                    
        except Exception as e:
            self.logger.error(f"处理表切换失败: {e}")
    
    def _extract_table_name_from_path(self, path: str) -> Optional[str]:
        """从导航路径提取表名"""
        try:
            # 这里需要根据实际的路径格式来解析
            # 暂时使用简单的解析逻辑
            if "/" in path:
                parts = path.split("/")
                # 假设表名在路径的最后一部分
                return parts[-1] if parts[-1] else parts[-2]
            return path
        except Exception as e:
            self.logger.error(f"解析表名失败: {e}")
            return None
    
    def find_tables_by_pattern(self, pattern: str) -> List[str]:
        """
        根据模式查找匹配的表名

        Args:
            pattern: 表名模式，支持部分匹配

        Returns:
            匹配的表名列表
        """
        try:
            # 获取所有表
            all_tables = []

            # 从数据库管理器获取表列表
            if self._db_manager:
                try:
                    # 获取所有工资数据表
                    tables = self._db_manager.get_table_list(table_type='salary_data')
                    if tables:
                        all_tables = [table.get('name', '') for table in tables if table.get('name')]
                except Exception as e:
                    self.logger.warning(f"从数据库管理器获取表列表失败: {e}")

            # 如果没有从数据库管理器获取到表，尝试从缓存中获取
            if not all_tables:
                # 从缓存中提取表名
                cache_tables = set()
                for key in self._data_cache.keys():
                    parts = key.split("_")
                    if len(parts) > 0:
                        cache_tables.add(parts[0])
                all_tables.extend(list(cache_tables))

            # 筛选匹配的表
            matching_tables = [table for table in all_tables if pattern in table]

            # 按表名排序
            matching_tables.sort()

            self.logger.info(f"[表名查找] 模式 '{pattern}' 匹配到 {len(matching_tables)} 个表")
            return matching_tables

        except Exception as e:
            self.logger.error(f"查找表失败: {e}")
            return []

    def _extract_table_type_from_name(self, table_name: str) -> str:
        """
        [统一格式管理] 从表名中提取表格类型
        
        Args:
            table_name: 表名
            
        Returns:
            表格类型标识
        """
        try:
            # 根据表名模式匹配表格类型
            table_name_lower = table_name.lower()
            
            if 'active_employees' in table_name_lower or '全部在职人员' in table_name:
                return 'active_employees'
            elif 'retired_employees' in table_name_lower or '离休人员' in table_name:
                return 'retired_employees'
            elif 'pension_employees' in table_name_lower or '退休人员' in table_name:
                return 'pension_employees'  # 退休人员使用独立的格式配置
            elif 'part_time' in table_name_lower or '临时工' in table_name:
                return 'part_time_employees'
            elif 'contract' in table_name_lower or '合同工' in table_name:
                return 'contract_employees'
            elif 'a_grade' in table_name_lower or 'a岗' in table_name:
                return 'a_grade_employees'  # A岗职工使用专用格式
            else:
                # 默认使用在职人员格式
                self.logger.warning(f"[统一格式管理] 未识别的表类型: {table_name}, 使用默认格式")
                return 'active_employees'
                
        except Exception as e:
            self.logger.error(f"[统一格式管理] 表类型提取失败: {e}")
            return 'active_employees'

    def get_service_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            return {
                "current_table": self._current_table,
                "cached_tables": len(set(key.split("_")[0] for key in self._data_cache.keys())),
                "cache_entries": len(self._data_cache),
                "loading_tables": list(self._loading_states.keys()),
                "data_request_stats": self.data_request_manager.get_request_statistics(),
                "state_manager_stats": self.state_manager.get_state_statistics(),
                "event_bus_stats": self.event_bus.get_statistics()
            }
        except Exception as e:
            self.logger.error(f"获取服务统计失败: {e}")
            return {"error": str(e)} 
    def get_cache_performance_stats(self) -> dict:
        """[性能监控] 获取缓存性能统计"""
        total = self._cache_stats["total_requests"]
        hit_rate = (self._cache_stats["hits"] / total * 100) if total > 0 else 0
        
        return {
            "cache_size": len(self._data_cache),
            "total_requests": total,
            "cache_hits": self._cache_stats["hits"],
            "cache_misses": self._cache_stats["misses"],
            "hit_rate_percent": f"{hit_rate:.1f}%",
            "evictions": self._cache_stats["evictions"]
        }
    
    def log_cache_performance(self):
        """[性能监控] 记录缓存性能"""
        stats = self.get_cache_performance_stats()
        self.logger.info(f"[缓存统计] {stats}")

